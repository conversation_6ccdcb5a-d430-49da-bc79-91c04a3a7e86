'use client';

import React, { useState, useEffect, useRef } from "react";
import { Send, Heart } from "lucide-react";
import { motion } from "framer-motion";
import ReactMarkdown from 'react-markdown';
import VoiceSelector, { Voice } from "./voiceSelector";
import ThemeSelector from "./ThemeSelector";
import StoreSession from "./StoreSession";
import SignOut from "./SignOut";
import { createBrowserClient } from '@supabase/ssr';
import LegalDrawer from './LegalDrawer';

console.count("💬 ChatWindow rendered");

type Message = {
  role: "user" | "assistant";
  content: string;
};

type Props = {
  summaries: string[];
};


export default function ChatWindow({ summaries }: Props) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const chatRef = useRef<HTMLDivElement>(null);
  const [voice, setVoice] = useState<Voice>('calm');
  const [userName, setUserName] = useState('');

  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  useEffect(() => {
    const getUserName = async () => {
      const userRes = await supabase.auth.getUser();
      const name = userRes.data.user?.user_metadata?.name;
      setUserName(name ?? null);
    };

    getUserName();
  }, []);

  // Sleep delay for token pacing
  function sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // start prompts
  const starterPrompts = [
    "I need to get this off my chest.",
    "I’ve been feeling anxious.",
    "I’m proud but still unsettled.",
    "It’s all been a bit much lately.",
    "I had a good day today.",
    "I feel stuck in my head.",
    "I made some real progress.",
    "I just want to be heard.",
  ];

  function handleStarterPrompt(prompt: string) {
    setInput(prompt);
    sendMessage(prompt);
  }

  async function sendMessage(promptOverride?: string) {
    const inputToSend = promptOverride ?? input;
    if (!inputToSend.trim()) return;

    const userMessage: Message = { role: "user", content: inputToSend };
    const botMessage: Message = { role: "assistant", content: "" };
    setMessages(prev => [...prev, userMessage, botMessage]);
    setInput("");
    setLoading(true);

    let streamedContent = "";

    const assistantPrompt = `
    Use the following memory of past sessions to guide your response:

    ${summaries.map((s) => `• ${s}`).join('\n')}

    Now respond thoughtfully to the user’s message:
    `;

    await streamAssistantReply(inputToSend, (token) => {
      streamedContent += token;
      setMessages(prev => {
        const updated = [...prev];
        updated[updated.length - 1] = {
          role: "assistant",
          content: streamedContent,
        };
        return updated;
      });
    }, assistantPrompt);

    setLoading(false);
  }

  async function streamAssistantReply(message: string, onToken: (token: string) => void, context?: string) {
    const res = await fetch("/api/chat", {
      method: "POST",
      body: JSON.stringify({ message, voice, context }),
      headers: { "Content-Type": "application/json" },
    });

    if (!res.body) return;

    const reader = res.body.getReader();
    const decoder = new TextDecoder("utf-8");

    while (true) {
      const { value, done } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });

      // Stream each character/token with a slight delay
      for (const token of chunk) {
        onToken(token);
        await sleep(30); // Adjust for pacing
      }
    }
  }

  function handleKeyDown(e: React.KeyboardEvent<HTMLInputElement>) {
    if (e.key === "Enter") sendMessage();
  }

  return (
    <div className="flex flex-col h-full p-4">
      <div ref={chatRef} className="window relative justify-space-around flex flex-wrap content-start flex-grow rounded-lg overflow-y-auto space-y-3">
        {messages.length === 0 && !loading && (
          <div className="text-center height text-gray-500 max-w-xl mx-auto space-y-4">
            <p className="italic">
              { userName
               ? `Hello, ${userName}, I’m somii — your space to think, feel, and reflect. There’s no rush. When you’re ready, choose a topic or start typing. Feel free to change my approach by selecting a different voice or change themes to fit your mood.`
                : `Hello, I’m somii — your space to think, feel, and reflect. There’s no rush. When you’re ready, choose a topic or start typing. Feel free to change my approach by selecting a different voice or change themes to fit your mood.` }
               </p>

            <div className="topic-wrapper mt-10 flex flex-wrap justify-center gap-2">
              {starterPrompts.map((prompt, idx) => (
                <button
                  key={idx}
                  onClick={() => handleStarterPrompt(prompt)}
                  className="bg-gray-100 border text-sm px-4 py-2 rounded-4xl hover:bg-gray-200 transition hover:cursor-pointer"
                >
                  {prompt}
                </button>
              ))}
            </div>
          </div>
        )}
        {messages.map((msg, idx) => {
          if (msg.role === "assistant" && !msg.content.trim()) return null;

          return (
            <div
              key={idx}
              className={`flex w-full ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className={`p-4 rounded-lg max-w-[80%] ${
                  msg.role === "user"
                    ? "text-right"
                    : "bg-white text-left"
                }`}
              >
                <ReactMarkdown
                  components={{
                    p: ({ children }) => (
                      <p className="mb-2 text-base leading-relaxed">{children}</p>
                    ),
                    strong: ({ children }) => (
                      <strong className="font-semibold">{children}</strong>
                    ),
                    ul: ({ children }) => (
                      <ul className="list-disc list-inside space-y-1">{children}</ul>
                    ),
                    ol: ({ children }) => (
                      <ol className="list-decimal list-inside space-y-1">{children}</ol>
                    ),
                    li: ({ children }) => (
                      <li className="ml-4">{children}</li>
                    ),
                    h1: ({ children }) => (
                      <h1 className="text-xl font-bold mb-2">{children}</h1>
                    ),
                    h2: ({ children }) => (
                      <h2 className="text-lg font-semibold mb-2">{children}</h2>
                    ),
                  }}
                >
                  {msg.content}
                </ReactMarkdown>
              </motion.div>
            </div>
          );
        })}
        {loading && (
          <div className="text-sm text-gray-400 italic">SOMII is thinking...</div>
        )}
      </div>

      


      <div className="mt-2 relative main-input">
        <div className="text-input">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="What's on your mind?"
            className="w-full rounded px-4 py-2 border"
          />
          <button
            onClick={() => sendMessage()}
            className="send-btn text-white text-sm px-4 py-2 transition"
          >
            <Send />
          </button>
        </div>

        <div className="option-input">
            <VoiceSelector voice={voice} setVoice={setVoice} />
            <ThemeSelector />
            <StoreSession messages={messages} voice={voice} />
        </div>

        <SignOut />
        <LegalDrawer /> 
        <p className="jon hidden sm:block">built with <Heart size={14} className="inline-block" /> by <a href="https://x.com/JontheNerd_" target="_blank" rel="noopener noreferrer" className="underline">Jon</a></p>
      </div>
    </div>
  );
}
