import { createBrowserClient } from '@supabase/ssr';

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export async function fetchUserSummaries(userId: string, limit = 5) {
  const { data, error } = await supabase
    .from('session_summaries')
    .select('summary')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('❌ Failed to fetch summaries:', error.message);
    return [];
  }

  return data.map(d => d.summary);
}
