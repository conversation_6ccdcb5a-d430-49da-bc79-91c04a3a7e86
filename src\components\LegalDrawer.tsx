'use client';

import { useState } from 'react';
import { X } from 'lucide-react';

const privacyHTML = `
 <h1>Privacy Policy</h1>
  <p><strong>Last Updated:</strong> 5.19.2025</p>
    <br>
  <h2>1. Introduction</h2>
  <p>Welcome to Somii. Your privacy is important to us. This Privacy Policy explains how we collect, use, and protect your personal data when you use <PERSON><PERSON><PERSON>, your digital therapist companion.</p>
<br>
  <h2>2. Information We Collect</h2>
  <ul>
    <li>Name and email address (used to create and manage your account)</li>
    <li>User-generated content (e.g., session messages and saved summaries)</li>
    <li>Authentication data via Supabase</li>
    <li>Usage metadata (browser type, access times, etc.)</li>
  </ul>
<br>
  <h2>3. How We Use Your Data</h2>
  <p>We use your information to:</p>
  <ul>
    <li>Personalize your experience</li>
    <li>Save and recall past session summaries</li>
    <li>Authenticate and manage your account</li>
    <li>Improve our platform</li>
  </ul>
<br>
  <h2>4. Data Storage and Security</h2>
  <p>Your data is securely stored via Supabase with access limited to authorized administrators.</p>
<br>
  <h2>5. Use of AI</h2>
  <p>Somii uses OpenAI's GPT models to generate responses. Your input is sent to OpenAI for processing. By using Somii, you consent to this processing.</p>
<br>
  <h2>6. Minors</h2>
  <p>Somii may be used by individuals under 18. We recommend parental supervision for younger users.</p>
<br>
  <h2>7. Data Retention</h2>
  <p>We retain session data as long as your account is active. Contact us to request deletion.</p>
<br>
  <h2>8. Contact Us</h2>
  <p>Email: <EMAIL></p>
`;

const termsHTML = `
  <h1>Terms of Use</h1>
  <p><strong>Last Updated:</strong> 5.19.2025</p>
<br>
  <h2>1. Agreement</h2>
  <p>By using Somii, you agree to these Terms of Use. If you do not agree, please do not use the app.</p>
<br>
  <h2>2. Nature of Service</h2>
  <p>Somii is a digital reflection companion powered by AI. It is not a licensed therapist or medical provider. Do not use it in emergencies.</p>
<br>
  <h2>3. User Responsibilities</h2>
  <ul>
    <li>Provide accurate account information</li>
    <li>Do not share abusive, illegal, or harmful content</li>
    <li>Keep your account credentials secure</li>
  </ul>
<br>
  <h2>4. Intellectual Property</h2>
  <p>All content on Somii (excluding your personal input) is owned by the creators of Somii.</p>
<br>
  <h2>5. Termination</h2>
  <p>We reserve the right to restrict or terminate access if these terms are violated.</p>
<br>
  <h2>6. Limitation of Liability</h2>
  <p>Somii is provided “as-is.” We are not liable for outcomes based on usage of the platform.</p>
<br>
  <h2>7. Contact</h2>
  <p>Email: <EMAIL></p>
`;

export default function LegalDrawer() {
  const [view, setView] = useState<'privacy' | 'terms' | null>(null);

  const content = view === 'privacy' ? privacyHTML : view === 'terms' ? termsHTML : '';

  return (
    <>
      <div className="flex gap-4 text-sm text-gray-500 mt-4 legal-links">
        <button onClick={() => setView('privacy')} className="hover:cursor-pointer hover:text-purple-600">
         Privacy Policy
        </button>
        <button onClick={() => setView('terms')} className="hover:cursor-pointer hover:text-purple-600">
         Terms of Use
        </button>
      </div>

      {view && (
        <div className="legal-info fixed inset-0 z-50 bg-black/40 flex justify-end items-stretch">
          <div className="bg-white w-full sm:w-[480px] h-full p-6 overflow-y-auto relative animate-slide-in">
            <button
              onClick={() => setView(null)}
              className="absolute top-4 right-4 text-gray-500 hover:text-black"
              aria-label="Close legal drawer"
            >
              <X />
            </button>
            <div dangerouslySetInnerHTML={{ __html: content }} />
          </div>
        </div>
      )}

      <style jsx>{`
        .animate-slide-in {
          animation: slideIn 0.3s ease-out forwards;
        }

        @keyframes slideIn {
          from {
            transform: translateX(100%);
          }
          to {
            transform: translateX(0);
          }
        }
      `}</style>
    </>
  );
}
