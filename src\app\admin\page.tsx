'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminPage() {
  const [isAuthed, setIsAuthed] = useState(false);
  const [enteredPass, setEnteredPass] = useState('');
  const router = useRouter();

  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [status, setStatus] = useState('');

  function checkPassword() {
    if (enteredPass === process.env.NEXT_PUBLIC_ADMIN_PASSWORD) {
      setIsAuthed(true);
    } else {
      alert("Incorrect password.");
    }
  }

  async function handleUpload() {
    setStatus('Uploading...');
    const res = await fetch('/api/upload', {
      method: 'POST',
      body: JSON.stringify({ title, content }),
      headers: { 'Content-Type': 'application/json' },
    });

    if (res.ok) setStatus('✅ Uploaded successfully!');
    else setStatus('❌ Upload failed.');
  }

  if (!isAuthed) {
    return (
      <div className="max-w-sm mx-auto p-6 space-y-4 text-center">
        <h1 className="text-lg font-semibold">Admin Login</h1>
        <input
          type="password"
          placeholder="Enter admin password"
          value={enteredPass}
          onChange={(e) => setEnteredPass(e.target.value)}
          className="w-full border p-2 rounded"
        />
        <button
          onClick={checkPassword}
          className="bg-black text-white px-4 py-2 rounded"
        >
          Enter
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-xl mx-auto p-6 space-y-4">
      <h1 className="text-xl font-semibold">Upload new somii content</h1>
      <input
        className="w-full bg-white p-2 rounded"
        placeholder="Title or topic..."
        value={title}
        onChange={(e) => setTitle(e.target.value)}
      />
      <textarea
        className="w-full bg-white p-2 rounded h-48"
        placeholder="Paste your new guide here..."
        value={content}
        onChange={(e) => setContent(e.target.value)}
      />
      <button
        className="bg-[#4a4e69] hover:cursor-pointer text-white px-4 py-2 rounded-4xl"
        onClick={handleUpload}
      >
        Upload
      </button>
      <button
        className="bg-[#4a4e69] hover:cursor-pointer text-white ml-2 px-4 py-2 rounded-4xl"
        onClick={() => router.push('/')}
        >
        Home
        </button>
      {status && <p className="text-sm text-gray-600">{status}</p>}
    </div>
  );
}
