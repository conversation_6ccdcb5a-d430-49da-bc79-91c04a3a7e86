import { NextRequest, NextResponse } from 'next/server';
import { openai } from '@/lib/openai';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(req: NextRequest) {
  let messages, voice, user_id;

  try {
    const body = await req.json(); // ✅ Only call this once!

    ({ messages, voice, user_id } = body); // ✅ Destructure from saved body
  } catch (err) {
    console.error('❌ Failed to read request body:', err);
    return NextResponse.json({ error: 'Bad request' }, { status: 400 });
  }

  const prompt = `
You are SOMII’s internal reflection engine.
Generate a JSON summary:

{
  "summary": "...",
  "emotional_tone": "...",
  "topic_tags": ["..."],
  "insights": "...",
  "voice_mode_used": "${voice}"
}

Conversation:
${messages.map((m: { role: string; content: string }) => `${m.role}: ${m.content}`).join('\n')}
`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [{ role: 'system', content: prompt }],
    });

    const parsed = JSON.parse(completion.choices[0].message.content || '{}');

    const { error } = await supabase.from('session_summaries').insert({
      user_id,
      ...parsed,
    });

    if (error) {
      console.error('❌ Supabase insert failed:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (err) {
    console.error('❌ GPT or save error:', err);
    return NextResponse.json({ error: 'Failed to generate summary.' }, { status: 500 });
  }
}
