'use client';

import { Listbox } from "@headlessui/react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { Fragment } from "react";
import { ReactElement } from "react";

export type Voice = "calm" | "coach" | "tough";

const voices: { value: Voice; label: string; icon: ReactElement }[] = [
  { value: "calm", label: "Calm Companion", icon: <Bot size={20} className="" /> },
  { value: "coach", label: "Reflective Coach", icon: <Brain size={20} className="" /> },
  { value: "tough", label: "Truthful Friend", icon: <Zap size={20} className="" /> },
];

export default function VoiceSelector({
  voice,
  setVoice,
}: {
  voice: Voice;
  setVoice: (v: Voice) => void;
}) {
  const selected = voices.find((v) => v.value === voice);

  return (
    <Listbox value={voice} onChange={setVoice}>
      <div className="relative w-45 voice-wrapper">
        <Listbox.Button className="flex items-center justify-between w-full rounded-full bg-[#2c2f48] px-4 py-2 text-white text-sm ">
          <div className="flex items-center gap-2 hover:cursor-pointer">
            {selected?.icon}
            <span className="hidden sm:inline">{selected?.label}</span>
          </div>
        </Listbox.Button>

        <Listbox.Options className="absolute bottom-full mb-2 z-10 w-full rounded-md bg-[#2c2f48] shadow-lg ring-1 ring-white/10 focus:outline-none text-sm text-white">
          {voices.map((voiceOption) => (
            <Listbox.Option key={voiceOption.value} value={voiceOption.value} as={Fragment}>
              {({ active }) => (
                <li
                  className={`cursor-pointer px-4 py-2 flex items-center gap-2 ${
                    active ? "bg-[#4a4e69]" : ""
                  }`}
                >
                  {voiceOption.icon}
                  <span className="hidden sm:inline">{voiceOption.label}</span>
                </li>
              )}
            </Listbox.Option>
          ))}
        </Listbox.Options>
      </div>
    </Listbox>
  );
}
