import { NextRequest, NextResponse } from 'next/server';
import { openai } from '@ai-sdk/openai';
import { embed } from 'ai';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Basic chunking helper
function chunkText(text: string, maxTokens = 500): string[] {
  const sentences = text.split(/(?<=[.?!])\s+/);
  const chunks: string[] = [];
  let current = '';

  for (const sentence of sentences) {
    if ((current + sentence).split(' ').length > maxTokens) {
      chunks.push(current.trim());
      current = sentence;
    } else {
      current += ' ' + sentence;
    }
  }

  if (current) chunks.push(current.trim());
  return chunks;
}

export async function POST(req: NextRequest) {
  const { content, title } = await req.json();

  if (!content || !title) {
    return NextResponse.json({ error: 'Missing content or title' }, { status: 400 });
  }

  const chunks = chunkText(content);

  for (const chunk of chunks) {
    const { embedding } = await embed({
      model: openai.embedding('text-embedding-3-small'),
      value: chunk,
    });

    const { error } = await supabase.from('rag_knowledge').insert([
      {
        content: chunk,
        embedding,
        source: title,
      },
    ]);

    if (error) {
      console.error('Failed to insert chunk:', error);
      return NextResponse.json({ error: 'Insert failed' }, { status: 500 });
    }
  }

  return NextResponse.json({ success: true });
}
