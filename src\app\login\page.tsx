'use client';

import { useState } from 'react';
import { createBrowserClient } from '@supabase/ssr';
import Swal from 'sweetalert2';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function AuthPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [loginType, setLoginType] = useState('login');
  const router = useRouter();

  async function handleLogin() {
    const { data, error } = await supabase.auth.signInWithPassword({ email, password });

    if (error) {
      Swal.fire({
        icon: 'error',
        title: 'Login Failed',
        text: error.message,
      });
      return;
    }

    if (data?.user) {
      Swal.fire({
        icon: 'success',
        title: 'Welcome back!',
        text: `Hi ${data.user.user_metadata?.name || 'there'}, you’re now logged in.`,
        timer: 3000,
        showConfirmButton: false,
      });
      router.push('/'); // redirect to home or dashboard
    }
  }

  async function handleSignup() {
  const { data, error } = await supabase.auth.signUp({ 
    email, 
    password,
      options: {
      data: {
        name: name.trim()
      }
    }
  });

  if (error) {
    alert(error.message);
    return;
  }

  if (!data.session) {
    Swal.fire({
      icon: 'info',
      title: 'Confirm Your Email',
      text: 'Check your inbox to finish signing up.',
    });
  } else {
    Swal.fire({
      icon: 'success',
      title: 'Signed up!',
      text: 'You’re logged in and ready to go.',
    });
  }
}

  return (
    <div className="login absolute top-1/3 left-1/2 -translate-x-1/2 -translate-y-1/2 max-w-sm mx-auto space-y-4 p-6">
      <Image
              className="login-logo"
              src="/somii-logo.png"
              alt="Somii logo"
              width={150}
              height={150}
            />
            <p className="login-tagline">Your digital companion.</p>
      {loginType === 'signup' && (
        <input
          className="w-full p-2 border rounded"
          placeholder="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
      )}

      <input
        className="w-full p-2 border rounded"
        placeholder="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />
      <input
        className="w-full p-2 border rounded"
        placeholder="password"
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />
      <div className="flex gap-4">
        {loginType === 'login' && (
          <>
            <button onClick={handleLogin} className="hover:cursor-pointer bg-purple-600 text-white px-4 py-2 rounded login-btn">
              Login
            </button>
            <button className="switch-login hover:cursor-pointer" onClick={() => setLoginType('signup')}>Create an account</button>
          </>

        )}
        {loginType === 'signup' && (
          <>
            <button onClick={handleSignup} className="bg-gray-300 px-4 py-2 rounded signup-btn">
              Sign Up
            </button>
            <button className="switch-login hover:cursor-pointer" onClick={() => setLoginType('login')}>Login</button>
          </>
        )}

      </div>
    </div>

  );
}
