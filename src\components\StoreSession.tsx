'use client';

import { useState } from 'react';
import <PERSON>wal from 'sweetalert2';
import { createBrowserClient } from '@supabase/ssr';
import { Upload } from 'lucide-react';

type Message = {
  role: 'user' | 'assistant';
  content: string;
};

type Props = {
  messages: Message[];
  voice: string;
};

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function StoreSession({ messages, voice }: Props) {
  const [saving, setSaving] = useState(false);

  async function handleFinishSession() {
    if (!messages.length) return;

    setSaving(true);

    const userRes = await supabase.auth.getUser();
    const user = userRes.data.user;

    if (!user) {
        Swal.fire({
        icon: 'warning',
        title: 'Not logged in',
        text: 'Please log in to save your session.',
        });
        setSaving(false);
        return;
    }

    const res = await fetch('/api/save-summary', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
        messages,
        voice,
        user_id: user.id,
        }),
    });

    const data = await res.json();

    if (res.ok) {
        Swal.fire({
        toast: true,
        position: 'top-end',
        icon: 'success',
        showConfirmButton: false,
        timer: 4000,
        timerProgressBar: true,
        text: 'somii has stored this reflection.',
        });
    } else {
        Swal.fire({
        toast: true,
        position: 'top-end',
        icon: 'error',
        timer: 4000,
        showConfirmButton: false,
        timerProgressBar: true,
        text: data.error || 'Something went wrong.',
        });
    }

    setSaving(false);
    }

  return (
    <button
  onClick={handleFinishSession}
  disabled={saving}
  className="flex items-center hover:cursor-pointer gap-2 text-sm px-4 py-2 rounded-full bg-[#1f1d2b] text-white hover:bg-[#2a2935] transition"
>
  <Upload size={20} className="" />
  {saving ? (
    <>
      <span>Saving...</span>
      <svg
        className="animate-spin h-4 w-4 text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        ></circle>
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
        ></path>
      </svg>
    </>
  ) : (
    <span className="hidden sm:inline">Save Session</span>
  )}
</button>

  );
}
