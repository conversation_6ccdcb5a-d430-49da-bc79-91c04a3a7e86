'use client';

import { Listbox } from '@headlessui/react';
import { Moon, Palette, Leaf, Sun } from 'lucide-react';
import { useEffect, useState, Fragment } from 'react';
import { ReactElement } from 'react';

export type Theme = 'default' | 'dark' | 'calm' | 'shine';

const themes: { value: Theme; label: string; icon: ReactElement }[] = [
  { value: 'default', label: 'Light', icon: <Palette size={20} className="" /> },
  { value: 'dark', label: 'Dark', icon: <Moon size={20} className="" /> },
  { value: 'calm', label: 'Calm', icon: <Leaf size={20} className="" /> },
  { value: 'shine', label: 'Shine', icon: <Sun size={20} className="" /> },
];

export default function ThemeSelector() {
  const [theme, setTheme] = useState<Theme>('default');

  // Load saved theme from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('somii-theme') as Theme | null;
    if (saved) {
      setTheme(saved);
      document.documentElement.className = saved;
    }
  }, []);

  // Apply theme and persist
  function applyTheme(newTheme: Theme) {
    setTheme(newTheme);
    document.documentElement.className = newTheme;
    localStorage.setItem('somii-theme', newTheme);
  }

  const selected = themes.find((t) => t.value === theme);

  return (
    <Listbox value={theme} onChange={applyTheme}>
      <div className="relative w-25 theme-wrapper">
        <Listbox.Button className="flex items-center justify-between w-full rounded-full bg-[#2c2f48] px-4 py-2 text-white text-sm hover:cursor-pointer">
          <div className="flex items-center gap-2">
            {selected?.icon}
            <span className="hidden sm:inline">{selected?.label}</span>
          </div>
        </Listbox.Button>

        <Listbox.Options className="absolute bottom-full mb-2 z-10 w-full rounded-md bg-[#2c2f48] shadow-lg ring-1 ring-white/10 focus:outline-none text-sm text-white hover:cursor-pointer">
          {themes.map((themeOption) => (
            <Listbox.Option key={themeOption.value} value={themeOption.value} as={Fragment}>
              {({ active }) => (
                <li
                  className={`cursor-pointer px-4 py-2 flex items-center gap-2 ${
                    active ? 'bg-[#4a4e69]' : ''
                  }`}
                >
                  {themeOption.icon}
                  <span className="hidden sm:inline">{themeOption.label}</span>
                </li>
              )}
            </Listbox.Option>
          ))}
        </Listbox.Options>
      </div>
    </Listbox>
  );
}
