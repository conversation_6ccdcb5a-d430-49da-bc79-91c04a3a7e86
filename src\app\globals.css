@import "tailwindcss";

:root {
  --textcolor: #22223b;
  --background: #e4d9d2;
  --backgroundTwo: #fff;
  --primary: #4a4e69;
  --secondary: #9a8c98;
  --accent: #c9ada7;
  --input: #fff;
  --button: #4a4e69;
}

html.dark {
  --textcolor: #f2e9e4;
  --backgroundTwo: #4a4e69;
  --background: #22223b;
  --primary: #4a4e69;
  --secondary: #9a8c98;
  --accent: #c9ada7;
  --input: #4a4e69;
  --button: #22223b;
}

html.calm {
   --textcolor: #fefdfc;             /* soft ivory white */
  --background: #5a6f66;            /* muted eucalyptus green */
  --backgroundTwo: #7f948c;         /* sage green-gray for panels/inputs */
  --primary: #c4d4c3;               /* gentle mint-sage for highlights */
  --secondary: #a7bcb3;             /* dusty green for buttons/borders */
  --accent: #e8e4da;                /* warm stone-cream accent */
  --input: #7f948c;
  --button: #a7bcb3;            
}

html.shine {
  --textcolor: #332f2e;           /* deep brown for grounding */
  --background: #fdf6e3;          /* soft cream base */
  --backgroundTwo: #fcebd5;       /* warm sand for input/panel contrast */
  --primary: #f4a261;             /* warm orange — optimistic, cozy */
  --secondary: #e76f51;           /* coral — adds emotional richness */
  --accent: #f6bd60;              /* golden honey — subtle brightness */
  --input: #fcebd5;               /* blends with backgroundTwo */
  --button: #e76f51;              /* bold enough to pop, still warm */
}

html.focus {
  --textcolor: #e0e0f0;           /* muted light blue-gray */
  --background: #1b1d2a;          /* near-black navy */
  --backgroundTwo: #2c2e3e;       /* slightly lighter panel/input tone */
  --primary: #5865f2;             /* soft blue-lavender — calm + focused */
  --secondary: #5e548e;           /* dusty purple — mature + grounded */
  --accent: #9fa4d2;              /* quiet lavender highlight */
  --input: #2c2e3e;               /* consistent with panel */
  --button: #5865f2;              /* subtle pop on dark background */
}

body {
  background: var(--background);
  color: var(--textcolor);
}

.logo {
    position: absolute;
    top: 15px;
    width: 120px;
}

.main-wrapper {
  height: calc(100vh - 100px);
}

.login-logo {
    margin: 0 auto 30px;
}

.login {
  background: var(--primary);
  border-radius: 12px;
  box-shadow: 0 2px 5px #aaa
}

.login-tagline {
  color: rgba(255,255,255,.75);
  text-align: center;
}

.login input {
  border: none;
  background: #fff;
  border-radius: 50px;
  padding: 10px 20px
}

.login-btn, .signup-btn {
  background: var(--secondary);
  color: #fff;
  border-radius: 50px;
  transition: linear .1s;
}

.login-btn:hover, .signup-btn:hover {
  background: var(--accent);
  cursor: pointer;
}

.switch-login {
  color: rgba(255,255,255,.75)
}

.switch-login:hover {
  text-decoration: underline;
}

.italic {
  color: var(--textcolor);
  opacity: 0.6;
}

.main-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--input);
  border-radius: 28px;
  padding: 10px;
  flex-wrap: wrap;
}

.main-wrapper input {
  background: var(--input);
  color: var(--textcolor);
  border: none;
  flex-grow: 1;
  margin-right: 10px;
  font-size: 16px;
  padding: 10px;
}

.main-wrapper input::placeholder {
  color: var(--textcolor);
  opacity: 0.5;
}

.text-input {
  display: flex;
  align-items: center;
  flex-grow: 1;
  width: 100%;
}

.voice-select, .theme-select {
  background: var(--background);
  color: var(--textcolor);
  border: none;
  font-size: 16px;
  padding: 5px 10px 5px 40px;
  border-radius: 28px;
  cursor: pointer;
}

.option-input {
    display: flex;
    gap: 5px;
}

.option-input button {
    background: var(--button);
}

.option-input button:hover {
    background: var(--secondary);
}

.voice-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.topic-wrapper button {
  width: 48%;
  background: var(--backgroundTwo);
  border: none;
  color: var(--textcolor);
}

.topic-wrapper button:hover {
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

input:focus {
  outline: none;
}

.window {
  justify-content: space-around;
}

.text-right {
  color: var(--textcolor);
}

.text-left {
  background: var(--backgroundTwo);
  color: var(--textcolor);
  position: relative;
}
.text-left:after {
  content: "somii";
  position: absolute;
  top: -12px;
  left: 25px;
  font-size: 14px;
  color: var(--textcolor);
}

.send-btn {
  position: absolute;
  right: 7px;
  bottom: 7px;
  background: var(--secondary);
  cursor: pointer;
  border-radius: 50px;
  padding: 10px;
}

.send-btn:hover {
  background: var(--accent);
}

.signout {
  position: absolute;
  left: 35px;
  bottom: -30px;
  color: var(--textcolor);
  font-size: 13px;
}

.signout:hover {
  color: var(--accent);
  cursor: pointer;
}

.legal-links {
  position: absolute;
  left: 105px;
  bottom: -30px;
  color: var(--textcolor);
}

.legal-links button {
  font-size: 13px;
}

.legal-links button:hover {
  color: var(--accent);
}

.legal-info div {
  background: var(--backgroundTwo);
}

.jon {
  position: absolute;
    bottom: -30px;
    right: 30px;
    font-size: 13px;
}

/* globals.css */

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--secondary); /* match your neutral tone */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--accent);
}

@media(max-width: 640px) {
  .option-input {
    flex-wrap: wrap;
  }
  .italic {
    font-size: 13px;
    line-height: 1.2;
  }
  .topic-wrapper button {
    font-size: 10px;
    padding: 10px;
  }
  .voice-wrapper, .theme-wrapper {
    width: auto;
  }
  .login {
    width: 90%;
    padding: 20px;
  }
}
