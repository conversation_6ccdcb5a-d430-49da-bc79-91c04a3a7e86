// scripts/embed.ts
import fs from 'fs';
import path from 'path';
import OpenAI from 'openai';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

dotenv.config();

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY! });
const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function generateEmbeddings() {
  const filePath = path.join(__dirname, '../src/data/therapist_guide.md');
  const fileText = fs.readFileSync(filePath, 'utf-8');

  // Split on horizontal rules or markdown dividers
  const chunks = fileText.split(/\n---+\n/).map(chunk => chunk.trim()).filter(Boolean);

  console.log(`⏳ Found ${chunks.length} chunks`);

  for (const chunk of chunks) {
    const embeddingRes = await openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: chunk,
    });

    const [{ embedding }] = embeddingRes.data;

    const { error } = await supabase.from('rag_knowledge').insert([
      {
        content: chunk,
        embedding,
      },
    ]);

    if (error) {
      console.error('❌ Error inserting into Supabase:', error);
    } else {
      console.log('✅ Inserted chunk.');
    }

    // Optionally delay between requests to avoid rate limit
    await new Promise((res) => setTimeout(res, 200));
  }

  console.log('🎉 All chunks embedded and uploaded.');
}

generateEmbeddings();
