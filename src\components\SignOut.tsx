'use client';

import { useRouter } from 'next/navigation';
import { createBrowserClient } from '@supabase/ssr';

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function SignOutButton() {
  const router = useRouter();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/login'); // Change to your actual login path if different
  };

  return (
    <button
      onClick={handleSignOut}
      className="signout text-sm hover:text-red-600 flex items-center gap-1"
    >
      Sign Out
    </button>
  );
}
