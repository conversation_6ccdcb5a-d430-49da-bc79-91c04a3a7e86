'use client';
import ChatWindow from "@/components/ChatWindow";
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { createBrowserClient } from '@supabase/ssr';
import Image from 'next/image';

const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export default function HomePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [summaries, setSummaries] = useState<string[]>([]);

  useEffect(() => {
    const init = async () => {
      const { data } = await supabase.auth.getUser();

      if (!data.user) {
        router.push('/login');
        return;
      }

      const res = await fetch('/api/get-summaries', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: data.user.id }),
      });

      const json = await res.json();
      setSummaries(json.summaries || []);
      setLoading(false);
    };

    init();
  }, [router]);

  if (loading) {
    return <div className="p-6 text-center height-full flex items-center justify-center">Loading SOMII...</div>;
  }
  return (
    
    <main className="flex min-h-screen items-center justify-center">
      <Image
        className="logo"
        src="/somii-logo-trans.png"
        alt="Somii logo"
        width={150}
        height={150}
      />
      <div className="main-wrapper w-full max-w-2xl rounded-xl">
        <ChatWindow summaries={summaries} />
      </div>
    </main>
  );
}
