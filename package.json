{"name": "somii", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@headlessui/react": "^2.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "ai": "^4.3.15", "dotenv": "^16.5.0", "framer-motion": "^12.11.4", "lucide-react": "^0.510.0", "next": "15.3.2", "openai": "^4.98.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "sweetalert2": "^11.21.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}