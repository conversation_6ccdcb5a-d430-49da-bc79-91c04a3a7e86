//import { OpenAI } from 'openai';
import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { embed } from 'ai';
import { createClient } from '@supabase/supabase-js';
import { NextRequest } from 'next/server';


const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(req: NextRequest) {
  const { message, voice, context } = await req.json();

  const { embedding } = await embed({
    model: openai.embedding('text-embedding-3-small'),
    value: message,
  });

  const { data: matches } = await supabase.rpc('match_rag_chunks', {
    query_embedding: embedding,
    match_threshold: 0.78,
    match_count: 5,
  });

  let tonePrompt = "";


    switch (voice) {
    case "calm":
      tonePrompt = `
  Speak like a warm, supportive friend. Be gentle, validating, and emotionally present. Use soft, reassuring language.
  Avoid sounding clinical or overly formal.`;
      break;
    case "coach":
      tonePrompt = `
  You're a curious and encouraging coach. Ask reflective, insight-driven questions.
  Help the user explore patterns and next steps without giving direct advice. Be confident, thoughtful, and kind.`;
      break;
    case "tough":
      tonePrompt = `
  You're a compassionate but no-nonsense mentor. Speak directly, honestly, and briefly.
  Don't apologize unless appropriate. Help the user see their own patterns and challenge avoidance. Avoid fluff.
  Examples: 
  - "That’s real. Now what are you gonna do with it?" 
  - "You’ve been here before. Let’s not stay stuck."`;
      break;
    default:
      tonePrompt = "";
  }

  const contextText = matches?.map((m: { content: string }) => m.content).join('\n---\n') ?? '';
  const combinedContext = `
  Session history:
  ${context ?? ''}

  Helpful knowledge from Somii's memory:
  ${contextText}
  `;


  const { textStream } = streamText({
  model: openai('gpt-4o'),
  messages: [
    {
      role: 'system',
      content: `
You are SOMII, a thoughtful and supportive digital companion trained in emotional intelligence, CBT, and mindfulness. 
You speak in a warm, conversational tone — like a trusted friend who really gets it.
Keep things simple, human, and relatable. Avoid clinical or formal language.
${tonePrompt}
Use the following helpful info to guide your responses:
${combinedContext}
      `.trim(),
    },
    {
      role: 'user',
      content: message,
    },
  ],
  temperature: 0.8,
});
return new Response(textStream, {
  headers: {
    'Content-Type': 'text/plain; charset=utf-8',
  },
});
}