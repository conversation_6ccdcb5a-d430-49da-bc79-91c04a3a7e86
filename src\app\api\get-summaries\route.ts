import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export async function POST(req: NextRequest) {
  const body = await req.json();
  const { user_id } = body;

  const { data, error } = await supabase
    .from('session_summaries')
    .select('summary')
    .eq('user_id', user_id)
    .order('created_at', { ascending: false })
    .limit(5);

  if (error) {
    console.error(error);
    return NextResponse.json({ summaries: [] }, { status: 500 });
  }

  return NextResponse.json({ summaries: data.map(d => d.summary) });
}
